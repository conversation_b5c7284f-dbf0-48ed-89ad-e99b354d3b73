#!/usr/bin/env python3
"""
Simplified Customer Churn Prediction Script
Uses only basic scikit-learn models for maximum compatibility.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def preprocess_data(df, is_training=True):
    """Comprehensive data preprocessing with feature engineering"""
    df_processed = df.copy()
    
    # Feature Engineering
    df_processed['Usage_Efficiency'] = df_processed['Total_Usage_Hours'] / (df_processed['Account_Age_Months'] + 1)
    df_processed['Spending_Per_Hour'] = df_processed['Monthly_Spending'] / (df_processed['Total_Usage_Hours'] + 1)
    df_processed['Support_Intensity'] = df_processed['Support_Calls'] / (df_processed['Account_Age_Months'] + 1)
    df_processed['Payment_Reliability'] = 1 / (df_processed['Late_Payments'] + 1)
    df_processed['Engagement_Score'] = (df_processed['Total_Usage_Hours'] * df_processed['Satisfaction_Score']) / (df_processed['Support_Calls'] + 1)
    
    # Age and spending categories
    df_processed['Age_Group'] = pd.cut(df_processed['Age'], bins=[0, 25, 35, 50, 100], labels=['Young', 'Adult', 'Middle', 'Senior'])
    df_processed['Spending_Category'] = pd.cut(df_processed['Monthly_Spending'], bins=3, labels=['Low', 'Medium', 'High'])
    
    # Handle categorical variables
    categorical_cols = ['Gender', 'Location', 'Subscription_Type', 'Last_Interaction_Type', 'Age_Group', 'Spending_Category']
    label_encoders = {}
    
    for col in categorical_cols:
        if col in df_processed.columns:
            le = LabelEncoder()
            df_processed[col] = le.fit_transform(df_processed[col].astype(str))
            label_encoders[col] = le
    
    # Remove Customer_ID
    if 'Customer_ID' in df_processed.columns:
        df_processed = df_processed.drop('Customer_ID', axis=1)
    
    return df_processed, label_encoders

def train_and_evaluate_model(model, X_train, X_val, y_train, y_val, model_name):
    """Train and evaluate a single model"""
    model.fit(X_train, y_train)
    y_pred = model.predict(X_val)
    
    accuracy = accuracy_score(y_val, y_pred)
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
    
    print(f"\n{model_name} Results:")
    print(f"Validation Accuracy: {accuracy:.4f}")
    print(f"CV Accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
    
    return model, accuracy, cv_scores.mean()

def main():
    print("🚀 Starting Customer Churn Prediction (Simplified Version)")
    print("=" * 60)
    
    # Load data
    print("📊 Loading data...")
    try:
        train_df = pd.read_csv('train.csv')
        test_df = pd.read_csv('test.csv')
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Please make sure train.csv and test.csv are in the current directory.")
        return
    
    print(f"Training data shape: {train_df.shape}")
    print(f"Test data shape: {test_df.shape}")
    print(f"Churn rate: {train_df['Churn'].mean():.2%}")
    
    # Preprocess data
    print("\n🔧 Preprocessing data...")
    train_processed, encoders = preprocess_data(train_df, is_training=True)
    test_processed, _ = preprocess_data(test_df, is_training=False)
    
    # Prepare features and target
    X = train_processed.drop('Churn', axis=1)
    y = train_processed['Churn']
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    # Feature scaling
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    print(f"Features: {X.shape[1]}")
    print(f"Training samples: {X_train.shape[0]}")
    print(f"Validation samples: {X_val.shape[0]}")
    
    # Train multiple models
    print("\n🤖 Training models...")
    results = {}
    
    # Models to try
    models = {
        'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
        'Gradient Boosting': GradientBoostingClassifier(random_state=42)
    }
    
    for name, model in models.items():
        trained_model, acc, cv = train_and_evaluate_model(
            model, X_train_scaled, X_val_scaled, y_train, y_val, name
        )
        results[name] = {'model': trained_model, 'accuracy': acc, 'cv_score': cv}
    
    # Find best model
    best_model_name = max(results, key=lambda x: results[x]['accuracy'])
    best_model = results[best_model_name]['model']
    best_accuracy = results[best_model_name]['accuracy']
    
    print(f"\n🏆 Best Model: {best_model_name}")
    print(f"🏆 Best Accuracy: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
    
    # Detailed evaluation of best model
    print(f"\n📊 Detailed evaluation of {best_model_name}:")
    y_pred_best = best_model.predict(X_val_scaled)
    print("\nClassification Report:")
    print(classification_report(y_val, y_pred_best))
    
    print("\nConfusion Matrix:")
    cm = confusion_matrix(y_val, y_pred_best)
    print(cm)
    
    # Calculate additional metrics
    tn, fp, fn, tp = cm.ravel()
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    print(f"\nAdditional Metrics:")
    print(f"Precision: {precision:.4f}")
    print(f"Recall (Sensitivity): {recall:.4f}")
    print(f"F1-Score: {f1_score:.4f}")
    print(f"Specificity: {specificity:.4f}")
    
    # Make predictions on test set
    print("\n📈 Making predictions on test set...")
    X_test_scaled = scaler.transform(test_processed)
    test_predictions = best_model.predict(X_test_scaled)
    test_probabilities = best_model.predict_proba(X_test_scaled)[:, 1]
    
    # Create submission
    submission = pd.DataFrame({
        'Customer_ID': test_df['Customer_ID'],
        'Churn_Prediction': test_predictions,
        'Churn_Probability': test_probabilities
    })
    
    submission.to_csv('churn_predictions.csv', index=False)
    
    print(f"\n✅ Results:")
    print(f"- Total test customers: {len(submission)}")
    print(f"- Predicted churners: {submission['Churn_Prediction'].sum()}")
    print(f"- Predicted churn rate: {submission['Churn_Prediction'].mean():.2%}")
    print(f"- Average churn probability: {submission['Churn_Probability'].mean():.4f}")
    print(f"- Predictions saved to: churn_predictions.csv")
    
    # Feature importance (if available)
    if hasattr(best_model, 'feature_importances_'):
        print(f"\n🔍 Top 10 Most Important Features:")
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': best_model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        for i, row in feature_importance.head(10).iterrows():
            print(f"{row['feature']}: {row['importance']:.4f}")
    
    print("\n🎉 Analysis Complete!")
    print("=" * 60)
    print(f"🎯 Final Best Model: {best_model_name}")
    print(f"🎯 Final Best Accuracy: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
    print("=" * 60)

if __name__ == "__main__":
    main()
