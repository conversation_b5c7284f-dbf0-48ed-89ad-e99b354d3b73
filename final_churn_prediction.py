#!/usr/bin/env python3
"""
Final Optimized Customer Churn Prediction Script
Uses ensemble methods with calibrated thresholds for optimal performance.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score, f1_score
from sklearn.calibration import CalibratedClassifierCV
import warnings
warnings.filterwarnings('ignore')

np.random.seed(42)

def preprocess_data(df):
    """Optimized data preprocessing"""
    df_processed = df.copy()
    
    # Core feature engineering
    df_processed['Usage_Efficiency'] = df_processed['Total_Usage_Hours'] / (df_processed['Account_Age_Months'] + 1)
    df_processed['Spending_Per_Hour'] = df_processed['Monthly_Spending'] / (df_processed['Total_Usage_Hours'] + 1)
    df_processed['Support_Intensity'] = df_processed['Support_Calls'] / (df_processed['Account_Age_Months'] + 1)
    df_processed['Payment_Reliability'] = 1 / (df_processed['Late_Payments'] + 1)
    df_processed['Engagement_Score'] = (df_processed['Total_Usage_Hours'] * df_processed['Satisfaction_Score']) / (df_processed['Support_Calls'] + 1)
    
    # Key interaction features
    df_processed['Age_Spending'] = df_processed['Age'] * df_processed['Monthly_Spending'] / 1000
    df_processed['Satisfaction_Support'] = df_processed['Satisfaction_Score'] / (df_processed['Support_Calls'] + 1)
    
    # Handle categorical variables
    categorical_cols = ['Gender', 'Location', 'Subscription_Type', 'Last_Interaction_Type']
    for col in categorical_cols:
        if col in df_processed.columns:
            le = LabelEncoder()
            df_processed[col] = le.fit_transform(df_processed[col].astype(str))
    
    if 'Customer_ID' in df_processed.columns:
        df_processed = df_processed.drop('Customer_ID', axis=1)
    
    return df_processed

def main():
    print("🚀 Final Optimized Customer Churn Prediction")
    print("=" * 60)
    
    # Load data
    print("📊 Loading data...")
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    
    print(f"Training data: {train_df.shape}, Test data: {test_df.shape}")
    print(f"Churn rate: {train_df['Churn'].mean():.2%}")
    
    # Preprocess
    print("\n🔧 Preprocessing data...")
    train_processed = preprocess_data(train_df)
    test_processed = preprocess_data(test_df)
    
    # Prepare data
    X = train_processed.drop('Churn', axis=1)
    y = train_processed['Churn']
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    print(f"Features: {X.shape[1]}, Training: {X_train.shape[0]}, Validation: {X_val.shape[0]}")
    
    # Train ensemble of models
    print("\n🤖 Training ensemble models...")
    
    # Individual models
    rf_model = RandomForestClassifier(n_estimators=100, max_depth=10, random_state=42, n_jobs=-1)
    gb_model = GradientBoostingClassifier(n_estimators=100, max_depth=5, random_state=42)
    lr_model = LogisticRegression(random_state=42, max_iter=1000, C=0.1)
    
    # Create ensemble
    ensemble = VotingClassifier(
        estimators=[('rf', rf_model), ('gb', gb_model), ('lr', lr_model)],
        voting='soft'
    )
    
    # Train models
    models = {
        'Random Forest': rf_model,
        'Gradient Boosting': gb_model,
        'Logistic Regression': lr_model,
        'Ensemble': ensemble
    }
    
    results = {}
    for name, model in models.items():
        print(f"\nTraining {name}...")
        model.fit(X_train_scaled, y_train)
        
        # Predictions
        y_pred = model.predict(X_val_scaled)
        y_proba = model.predict_proba(X_val_scaled)[:, 1]
        
        # Metrics
        accuracy = accuracy_score(y_val, y_pred)
        auc = roc_auc_score(y_val, y_proba)
        f1 = f1_score(y_val, y_pred)
        cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='roc_auc')
        
        results[name] = {
            'model': model,
            'accuracy': accuracy,
            'auc': auc,
            'f1': f1,
            'cv_auc': cv_scores.mean()
        }
        
        print(f"{name} - Accuracy: {accuracy:.4f}, AUC: {auc:.4f}, F1: {f1:.4f}, CV-AUC: {cv_scores.mean():.4f}")
    
    # Select best model based on AUC
    best_model_name = max(results, key=lambda x: results[x]['auc'])
    best_model = results[best_model_name]['model']
    
    print(f"\n🏆 Best Model: {best_model_name}")
    print(f"🏆 Best AUC: {results[best_model_name]['auc']:.4f}")
    print(f"🏆 Best Accuracy: {results[best_model_name]['accuracy']:.4f}")
    
    # Detailed evaluation
    y_pred_best = best_model.predict(X_val_scaled)
    y_proba_best = best_model.predict_proba(X_val_scaled)[:, 1]
    
    print(f"\n📊 Detailed evaluation:")
    print(classification_report(y_val, y_pred_best))
    
    # Find optimal threshold based on F1 score
    thresholds = np.arange(0.2, 0.8, 0.05)
    best_threshold = 0.5
    best_f1 = 0
    
    for threshold in thresholds:
        y_pred_thresh = (y_proba_best >= threshold).astype(int)
        f1_thresh = f1_score(y_val, y_pred_thresh)
        if f1_thresh > best_f1:
            best_f1 = f1_thresh
            best_threshold = threshold
    
    print(f"Optimal threshold: {best_threshold:.3f} (F1: {best_f1:.4f})")
    
    # Make final predictions
    print("\n📈 Making final predictions...")
    X_test_scaled = scaler.transform(test_processed)
    test_probabilities = best_model.predict_proba(X_test_scaled)[:, 1]
    test_predictions = (test_probabilities >= best_threshold).astype(int)
    
    # Create final submission files
    submission = pd.DataFrame({
        'Customer_ID': test_df['Customer_ID'],
        'Churn_Prediction': test_predictions,
        'Churn_Probability': test_probabilities.round(4)
    })
    
    # Detailed submission with customer insights
    detailed_submission = pd.DataFrame({
        'Customer_ID': test_df['Customer_ID'],
        'Age': test_df['Age'],
        'Gender': test_df['Gender'],
        'Location': test_df['Location'],
        'Subscription_Type': test_df['Subscription_Type'],
        'Monthly_Spending': test_df['Monthly_Spending'],
        'Total_Usage_Hours': test_df['Total_Usage_Hours'],
        'Satisfaction_Score': test_df['Satisfaction_Score'],
        'Support_Calls': test_df['Support_Calls'],
        'Late_Payments': test_df['Late_Payments'],
        'Churn_Prediction': test_predictions,
        'Churn_Probability': test_probabilities.round(4),
        'Risk_Level': ['High' if p >= 0.7 else 'Medium' if p >= 0.4 else 'Low' for p in test_probabilities],
        'Recommendation': [
            'Immediate retention action needed' if p >= 0.7 
            else 'Monitor and engage' if p >= 0.4 
            else 'Standard service' for p in test_probabilities
        ]
    })
    
    # Save results
    submission.to_csv('final_churn_predictions.csv', index=False)
    detailed_submission.to_csv('final_detailed_predictions.csv', index=False)
    
    # Summary statistics
    high_risk = sum(detailed_submission['Risk_Level'] == 'High')
    medium_risk = sum(detailed_submission['Risk_Level'] == 'Medium')
    low_risk = sum(detailed_submission['Risk_Level'] == 'Low')
    
    print(f"\n✅ Final Results:")
    print(f"- Total customers analyzed: {len(submission)}")
    print(f"- Predicted churners: {submission['Churn_Prediction'].sum()}")
    print(f"- Predicted churn rate: {submission['Churn_Prediction'].mean():.2%}")
    print(f"- Average churn probability: {submission['Churn_Probability'].mean():.4f}")
    print(f"- High risk customers: {high_risk} ({high_risk/len(submission)*100:.1f}%)")
    print(f"- Medium risk customers: {medium_risk} ({medium_risk/len(submission)*100:.1f}%)")
    print(f"- Low risk customers: {low_risk} ({low_risk/len(submission)*100:.1f}%)")
    
    print(f"\n📁 Files created:")
    print(f"- final_churn_predictions.csv (Customer_ID, Prediction, Probability)")
    print(f"- final_detailed_predictions.csv (Full customer data with insights)")
    
    print(f"\n🎯 Model Performance Summary:")
    print(f"- Best Model: {best_model_name}")
    print(f"- Validation Accuracy: {results[best_model_name]['accuracy']:.4f}")
    print(f"- AUC Score: {results[best_model_name]['auc']:.4f}")
    print(f"- F1 Score: {results[best_model_name]['f1']:.4f}")
    
    print("\n🎉 Final Analysis Complete!")
    print("=" * 60)

if __name__ == "__main__":
    main()
