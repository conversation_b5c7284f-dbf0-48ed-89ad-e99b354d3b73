#!/usr/bin/env python3
"""
Ultimate Customer Churn Prediction Script
This script implements comprehensive preprocessing and multiple ML models for churn prediction.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score
from sklearn.feature_selection import SelectKBest, f_classif
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostClassifier
import joblib
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def preprocess_data(df, is_training=True):
    """Comprehensive data preprocessing with feature engineering"""
    df_processed = df.copy()
    
    # Feature Engineering
    df_processed['Usage_Efficiency'] = df_processed['Total_Usage_Hours'] / (df_processed['Account_Age_Months'] + 1)
    df_processed['Spending_Per_Hour'] = df_processed['Monthly_Spending'] / (df_processed['Total_Usage_Hours'] + 1)
    df_processed['Support_Intensity'] = df_processed['Support_Calls'] / (df_processed['Account_Age_Months'] + 1)
    df_processed['Payment_Reliability'] = 1 / (df_processed['Late_Payments'] + 1)
    df_processed['Engagement_Score'] = (df_processed['Total_Usage_Hours'] * df_processed['Satisfaction_Score']) / (df_processed['Support_Calls'] + 1)
    
    # Age and spending categories
    df_processed['Age_Group'] = pd.cut(df_processed['Age'], bins=[0, 25, 35, 50, 100], labels=['Young', 'Adult', 'Middle', 'Senior'])
    df_processed['Spending_Category'] = pd.cut(df_processed['Monthly_Spending'], bins=3, labels=['Low', 'Medium', 'High'])
    
    # Handle categorical variables
    categorical_cols = ['Gender', 'Location', 'Subscription_Type', 'Last_Interaction_Type', 'Age_Group', 'Spending_Category']
    label_encoders = {}
    
    for col in categorical_cols:
        if col in df_processed.columns:
            le = LabelEncoder()
            df_processed[col] = le.fit_transform(df_processed[col].astype(str))
            label_encoders[col] = le
    
    # Remove Customer_ID
    if 'Customer_ID' in df_processed.columns:
        df_processed = df_processed.drop('Customer_ID', axis=1)
    
    return df_processed, label_encoders

def train_and_evaluate_model(model, X_train, X_val, y_train, y_val, model_name):
    """Train and evaluate a single model"""
    model.fit(X_train, y_train)
    y_pred = model.predict(X_val)
    y_pred_proba = model.predict_proba(X_val)[:, 1] if hasattr(model, 'predict_proba') else None
    
    accuracy = accuracy_score(y_val, y_pred)
    auc = roc_auc_score(y_val, y_pred_proba) if y_pred_proba is not None else None
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
    
    print(f"\n{model_name} Results:")
    print(f"Validation Accuracy: {accuracy:.4f}")
    print(f"AUC Score: {auc:.4f}" if auc else "AUC Score: N/A")
    print(f"CV Accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
    
    return model, accuracy, auc, cv_scores.mean()

def main():
    print("🚀 Starting Ultimate Customer Churn Prediction Challenge")
    print("=" * 60)
    
    # Load data
    print("📊 Loading data...")
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    
    print(f"Training data shape: {train_df.shape}")
    print(f"Test data shape: {test_df.shape}")
    print(f"Churn rate: {train_df['Churn'].mean():.2%}")
    
    # Preprocess data
    print("\n🔧 Preprocessing data...")
    train_processed, encoders = preprocess_data(train_df, is_training=True)
    test_processed, _ = preprocess_data(test_df, is_training=False)
    
    # Prepare features and target
    X = train_processed.drop('Churn', axis=1)
    y = train_processed['Churn']
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    # Feature scaling
    scaler = RobustScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    # Feature selection
    selector = SelectKBest(score_func=f_classif, k=15)
    X_train_selected = selector.fit_transform(X_train_scaled, y_train)
    X_val_selected = selector.transform(X_val_scaled)
    
    print(f"Selected {len(X.columns[selector.get_support()])} features")
    
    # Train multiple models
    print("\n🤖 Training multiple models...")
    results = {}
    
    # Models to try
    models = {
        'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
        'XGBoost': xgb.XGBClassifier(random_state=42, eval_metric='logloss'),
        'LightGBM': lgb.LGBMClassifier(random_state=42, verbose=-1),
        'CatBoost': CatBoostClassifier(random_state=42, verbose=False),
        'Gradient Boosting': GradientBoostingClassifier(random_state=42)
    }
    
    for name, model in models.items():
        if name in ['Logistic Regression']:
            trained_model, acc, auc, cv = train_and_evaluate_model(
                model, X_train_selected, X_val_selected, y_train, y_val, name
            )
        else:
            trained_model, acc, auc, cv = train_and_evaluate_model(
                model, X_train_scaled, X_val_scaled, y_train, y_val, name
            )
        results[name] = {'model': trained_model, 'accuracy': acc, 'auc': auc, 'cv_score': cv}
    
    # Find best model
    best_model_name = max(results, key=lambda x: results[x]['accuracy'])
    best_model = results[best_model_name]['model']
    best_accuracy = results[best_model_name]['accuracy']
    
    print(f"\n🏆 Best Model: {best_model_name}")
    print(f"🏆 Best Accuracy: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
    
    # Hyperparameter tuning for best models
    print("\n⚙️ Hyperparameter tuning...")
    
    if 'XGBoost' in results:
        xgb_params = {
            'n_estimators': [100, 200],
            'max_depth': [3, 4, 5],
            'learning_rate': [0.01, 0.1, 0.2]
        }
        xgb_grid = GridSearchCV(
            xgb.XGBClassifier(random_state=42, eval_metric='logloss'),
            xgb_params, cv=3, scoring='accuracy', n_jobs=-1
        )
        xgb_grid.fit(X_train_scaled, y_train)
        xgb_tuned_acc = accuracy_score(y_val, xgb_grid.best_estimator_.predict(X_val_scaled))
        
        if xgb_tuned_acc > best_accuracy:
            best_model = xgb_grid.best_estimator_
            best_accuracy = xgb_tuned_acc
            best_model_name = "XGBoost (Tuned)"
    
    print(f"\n🎯 Final Best Model: {best_model_name}")
    print(f"🎯 Final Best Accuracy: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
    
    # Make predictions on test set
    print("\n📈 Making predictions on test set...")
    X_test_scaled = scaler.transform(test_processed)
    test_predictions = best_model.predict(X_test_scaled)
    test_probabilities = best_model.predict_proba(X_test_scaled)[:, 1]
    
    # Create submission
    submission = pd.DataFrame({
        'Customer_ID': test_df['Customer_ID'],
        'Churn_Prediction': test_predictions,
        'Churn_Probability': test_probabilities
    })
    
    submission.to_csv('churn_predictions.csv', index=False)
    
    # Save model and preprocessing objects
    joblib.dump(best_model, 'best_churn_model.pkl')
    joblib.dump(scaler, 'scaler.pkl')
    joblib.dump(encoders, 'label_encoders.pkl')
    
    print(f"\n✅ Results:")
    print(f"- Predicted churners: {submission['Churn_Prediction'].sum()}")
    print(f"- Predicted churn rate: {submission['Churn_Prediction'].mean():.2%}")
    print(f"- Files saved: churn_predictions.csv, best_churn_model.pkl")
    
    print("\n🎉 Analysis Complete!")
    print("=" * 60)

if __name__ == "__main__":
    main()
