# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score
from sklearn.feature_selection import SelectKBest, f_classif
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostClassifier
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

print("Libraries imported successfully!")

# Load the data
train_df = pd.read_csv('train.csv')
test_df = pd.read_csv('test.csv')

print(f"Training data shape: {train_df.shape}")
print(f"Test data shape: {test_df.shape}")
print("\nTraining data info:")
print(train_df.info())
print("\nFirst few rows:")
print(train_df.head())

# Exploratory Data Analysis
print("Missing values in training data:")
print(train_df.isnull().sum())
print("\nMissing values in test data:")
print(test_df.isnull().sum())

print("\nChurn distribution:")
print(train_df['Churn'].value_counts())
print(f"Churn rate: {train_df['Churn'].mean():.2%}")

# Basic statistics
print("\nBasic statistics:")
print(train_df.describe())

# Visualization
plt.figure(figsize=(15, 10))

# Churn distribution
plt.subplot(2, 3, 1)
train_df['Churn'].value_counts().plot(kind='bar')
plt.title('Churn Distribution')
plt.xlabel('Churn')
plt.ylabel('Count')

# Age distribution by churn
plt.subplot(2, 3, 2)
train_df.boxplot(column='Age', by='Churn', ax=plt.gca())
plt.title('Age Distribution by Churn')

# Monthly spending by churn
plt.subplot(2, 3, 3)
train_df.boxplot(column='Monthly_Spending', by='Churn', ax=plt.gca())
plt.title('Monthly Spending by Churn')

# Satisfaction score by churn
plt.subplot(2, 3, 4)
train_df.boxplot(column='Satisfaction_Score', by='Churn', ax=plt.gca())
plt.title('Satisfaction Score by Churn')

# Support calls by churn
plt.subplot(2, 3, 5)
train_df.boxplot(column='Support_Calls', by='Churn', ax=plt.gca())
plt.title('Support Calls by Churn')

# Correlation heatmap
plt.subplot(2, 3, 6)
numeric_cols = train_df.select_dtypes(include=[np.number]).columns
correlation_matrix = train_df[numeric_cols].corr()
sns.heatmap(correlation_matrix, annot=False, cmap='coolwarm', center=0)
plt.title('Correlation Heatmap')

plt.tight_layout()
plt.show()

# Data Preprocessing Function
def preprocess_data(df, is_training=True):
    df_processed = df.copy()
    
    # Feature Engineering
    # 1. Usage efficiency
    df_processed['Usage_Efficiency'] = df_processed['Total_Usage_Hours'] / (df_processed['Account_Age_Months'] + 1)
    
    # 2. Spending per hour
    df_processed['Spending_Per_Hour'] = df_processed['Monthly_Spending'] / (df_processed['Total_Usage_Hours'] + 1)
    
    # 3. Support intensity
    df_processed['Support_Intensity'] = df_processed['Support_Calls'] / (df_processed['Account_Age_Months'] + 1)
    
    # 4. Payment reliability
    df_processed['Payment_Reliability'] = 1 / (df_processed['Late_Payments'] + 1)
    
    # 5. Engagement score
    df_processed['Engagement_Score'] = (df_processed['Total_Usage_Hours'] * df_processed['Satisfaction_Score']) / (df_processed['Support_Calls'] + 1)
    
    # 6. Age groups
    df_processed['Age_Group'] = pd.cut(df_processed['Age'], bins=[0, 25, 35, 50, 100], labels=['Young', 'Adult', 'Middle', 'Senior'])
    
    # 7. Spending categories
    df_processed['Spending_Category'] = pd.cut(df_processed['Monthly_Spending'], bins=3, labels=['Low', 'Medium', 'High'])
    
    # Handle categorical variables
    categorical_cols = ['Gender', 'Location', 'Subscription_Type', 'Last_Interaction_Type', 'Age_Group', 'Spending_Category']
    
    # Label encoding for categorical variables
    label_encoders = {}
    for col in categorical_cols:
        if col in df_processed.columns:
            le = LabelEncoder()
            df_processed[col] = le.fit_transform(df_processed[col].astype(str))
            label_encoders[col] = le
    
    # Remove Customer_ID as it's not useful for prediction
    if 'Customer_ID' in df_processed.columns:
        df_processed = df_processed.drop('Customer_ID', axis=1)
    
    return df_processed, label_encoders

# Apply preprocessing
train_processed, encoders = preprocess_data(train_df, is_training=True)
test_processed, _ = preprocess_data(test_df, is_training=False)

print("Data preprocessing completed!")
print(f"Processed training data shape: {train_processed.shape}")
print(f"Processed test data shape: {test_processed.shape}")

# Prepare features and target
X = train_processed.drop('Churn', axis=1)
y = train_processed['Churn']

# Split the data
X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

print(f"Training set shape: {X_train.shape}")
print(f"Validation set shape: {X_val.shape}")
print(f"Training set churn rate: {y_train.mean():.2%}")
print(f"Validation set churn rate: {y_val.mean():.2%}")

# Feature Scaling
scaler = RobustScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_val_scaled = scaler.transform(X_val)

# Feature Selection
selector = SelectKBest(score_func=f_classif, k=15)
X_train_selected = selector.fit_transform(X_train_scaled, y_train)
X_val_selected = selector.transform(X_val_scaled)

# Get selected feature names
selected_features = X.columns[selector.get_support()]
print(f"Selected features: {list(selected_features)}")
print(f"Number of selected features: {len(selected_features)}")

# Model Training and Evaluation Function
def train_and_evaluate_model(model, X_train, X_val, y_train, y_val, model_name):
    # Train the model
    model.fit(X_train, y_train)
    
    # Make predictions
    y_pred = model.predict(X_val)
    y_pred_proba = model.predict_proba(X_val)[:, 1] if hasattr(model, 'predict_proba') else None
    
    # Calculate metrics
    accuracy = accuracy_score(y_val, y_pred)
    auc = roc_auc_score(y_val, y_pred_proba) if y_pred_proba is not None else None
    
    # Cross-validation score
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
    
    print(f"\n{model_name} Results:")
    print(f"Validation Accuracy: {accuracy:.4f}")
    print(f"AUC Score: {auc:.4f}" if auc else "AUC Score: N/A")
    print(f"CV Accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
    
    return model, accuracy, auc, cv_scores.mean()

# Dictionary to store results
results = {}

print("Starting model training and evaluation...")

# 1. Logistic Regression
lr_model = LogisticRegression(random_state=42, max_iter=1000)
lr_trained, lr_acc, lr_auc, lr_cv = train_and_evaluate_model(
    lr_model, X_train_selected, X_val_selected, y_train, y_val, "Logistic Regression"
)
results['Logistic Regression'] = {'model': lr_trained, 'accuracy': lr_acc, 'auc': lr_auc, 'cv_score': lr_cv}

# 2. Random Forest
rf_model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
rf_trained, rf_acc, rf_auc, rf_cv = train_and_evaluate_model(
    rf_model, X_train_scaled, X_val_scaled, y_train, y_val, "Random Forest"
)
results['Random Forest'] = {'model': rf_trained, 'accuracy': rf_acc, 'auc': rf_auc, 'cv_score': rf_cv}

# 3. XGBoost
xgb_model = xgb.XGBClassifier(random_state=42, eval_metric='logloss')
xgb_trained, xgb_acc, xgb_auc, xgb_cv = train_and_evaluate_model(
    xgb_model, X_train_scaled, X_val_scaled, y_train, y_val, "XGBoost"
)
results['XGBoost'] = {'model': xgb_trained, 'accuracy': xgb_acc, 'auc': xgb_auc, 'cv_score': xgb_cv}

# 4. LightGBM
lgb_model = lgb.LGBMClassifier(random_state=42, verbose=-1)
lgb_trained, lgb_acc, lgb_auc, lgb_cv = train_and_evaluate_model(
    lgb_model, X_train_scaled, X_val_scaled, y_train, y_val, "LightGBM"
)
results['LightGBM'] = {'model': lgb_trained, 'accuracy': lgb_acc, 'auc': lgb_auc, 'cv_score': lgb_cv}

# 5. CatBoost
cat_model = CatBoostClassifier(random_state=42, verbose=False)
cat_trained, cat_acc, cat_auc, cat_cv = train_and_evaluate_model(
    cat_model, X_train_scaled, X_val_scaled, y_train, y_val, "CatBoost"
)
results['CatBoost'] = {'model': cat_trained, 'accuracy': cat_acc, 'auc': cat_auc, 'cv_score': cat_cv}

# 6. Gradient Boosting
gb_model = GradientBoostingClassifier(random_state=42)
gb_trained, gb_acc, gb_auc, gb_cv = train_and_evaluate_model(
    gb_model, X_train_scaled, X_val_scaled, y_train, y_val, "Gradient Boosting"
)
results['Gradient Boosting'] = {'model': gb_trained, 'accuracy': gb_acc, 'auc': gb_auc, 'cv_score': gb_cv}

# 7. Support Vector Machine
svm_model = SVC(random_state=42, probability=True)
svm_trained, svm_acc, svm_auc, svm_cv = train_and_evaluate_model(
    svm_model, X_train_selected, X_val_selected, y_train, y_val, "SVM"
)
results['SVM'] = {'model': svm_trained, 'accuracy': svm_acc, 'auc': svm_auc, 'cv_score': svm_cv}

# Results Summary
print("\n" + "="*60)
print("MODEL PERFORMANCE SUMMARY")
print("="*60)

results_df = pd.DataFrame({
    'Model': list(results.keys()),
    'Validation Accuracy': [results[model]['accuracy'] for model in results.keys()],
    'AUC Score': [results[model]['auc'] for model in results.keys()],
    'CV Score': [results[model]['cv_score'] for model in results.keys()]
})

results_df = results_df.sort_values('Validation Accuracy', ascending=False)
print(results_df.to_string(index=False))

# Find best model
best_model_name = results_df.iloc[0]['Model']
best_model = results[best_model_name]['model']
print(f"\nBest Model: {best_model_name}")
print(f"Best Accuracy: {results_df.iloc[0]['Validation Accuracy']:.4f}")

# Hyperparameter Tuning for Best Models
print("Starting hyperparameter tuning for top models...")

# XGBoost Hyperparameter Tuning
xgb_params = {
    'n_estimators': [100, 200, 300],
    'max_depth': [3, 4, 5, 6],
    'learning_rate': [0.01, 0.1, 0.2],
    'subsample': [0.8, 0.9, 1.0]
}

xgb_grid = GridSearchCV(
    xgb.XGBClassifier(random_state=42, eval_metric='logloss'),
    xgb_params,
    cv=3,
    scoring='accuracy',
    n_jobs=-1,
    verbose=1
)

xgb_grid.fit(X_train_scaled, y_train)
xgb_best = xgb_grid.best_estimator_

print(f"XGBoost Best Parameters: {xgb_grid.best_params_}")
print(f"XGBoost Best CV Score: {xgb_grid.best_score_:.4f}")

# Random Forest Hyperparameter Tuning
rf_params = {
    'n_estimators': [100, 200, 300],
    'max_depth': [10, 15, 20, None],
    'min_samples_split': [2, 5, 10],
    'min_samples_leaf': [1, 2, 4]
}

rf_grid = GridSearchCV(
    RandomForestClassifier(random_state=42, n_jobs=-1),
    rf_params,
    cv=3,
    scoring='accuracy',
    n_jobs=-1,
    verbose=1
)

rf_grid.fit(X_train_scaled, y_train)
rf_best = rf_grid.best_estimator_

print(f"Random Forest Best Parameters: {rf_grid.best_params_}")
print(f"Random Forest Best CV Score: {rf_grid.best_score_:.4f}")

# Ensemble Model - Voting Classifier
voting_clf = VotingClassifier(
    estimators=[
        ('xgb', xgb_best),
        ('rf', rf_best),
        ('lgb', lgb_trained),
        ('cat', cat_trained)
    ],
    voting='soft'
)

ensemble_trained, ensemble_acc, ensemble_auc, ensemble_cv = train_and_evaluate_model(
    voting_clf, X_train_scaled, X_val_scaled, y_train, y_val, "Ensemble (Voting)"
)

results['Ensemble'] = {'model': ensemble_trained, 'accuracy': ensemble_acc, 'auc': ensemble_auc, 'cv_score': ensemble_cv}

# Final Model Selection and Evaluation
print("\n" + "="*60)
print("FINAL MODEL COMPARISON (Including Tuned Models)")
print("="*60)

# Add tuned models to results
xgb_tuned_acc = accuracy_score(y_val, xgb_best.predict(X_val_scaled))
rf_tuned_acc = accuracy_score(y_val, rf_best.predict(X_val_scaled))

final_results = {
    'XGBoost (Tuned)': xgb_tuned_acc,
    'Random Forest (Tuned)': rf_tuned_acc,
    'Ensemble': ensemble_acc,
    'Original Best': results_df.iloc[0]['Validation Accuracy']
}

for model_name, acc in final_results.items():
    print(f"{model_name}: {acc:.4f}")

# Select the absolute best model
best_final_model_name = max(final_results, key=final_results.get)
best_final_accuracy = final_results[best_final_model_name]

print(f"\nFINAL BEST MODEL: {best_final_model_name}")
print(f"FINAL BEST ACCURACY: {best_final_accuracy:.4f}")

# Select the actual model object
if best_final_model_name == 'XGBoost (Tuned)':
    final_model = xgb_best
elif best_final_model_name == 'Random Forest (Tuned)':
    final_model = rf_best
elif best_final_model_name == 'Ensemble':
    final_model = ensemble_trained
else:
    final_model = best_model

# Feature Importance Analysis
if hasattr(final_model, 'feature_importances_'):
    feature_importance = pd.DataFrame({
        'feature': X.columns,
        'importance': final_model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\nTop 10 Most Important Features:")
    print(feature_importance.head(10).to_string(index=False))
    
    # Plot feature importance
    plt.figure(figsize=(10, 6))
    plt.barh(feature_importance.head(10)['feature'], feature_importance.head(10)['importance'])
    plt.title('Top 10 Feature Importances')
    plt.xlabel('Importance')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.show()
elif hasattr(final_model, 'estimators_'):
    # For ensemble models, try to get feature importance from first estimator
    if hasattr(final_model.estimators_[0], 'feature_importances_'):
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': final_model.estimators_[0].feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\nTop 10 Most Important Features (from first estimator):")
        print(feature_importance.head(10).to_string(index=False))

# Detailed Classification Report
y_pred_final = final_model.predict(X_val_scaled)
print("\nDetailed Classification Report:")
print(classification_report(y_val, y_pred_final))

# Confusion Matrix
cm = confusion_matrix(y_val, y_pred_final)
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['No Churn', 'Churn'], 
            yticklabels=['No Churn', 'Churn'])
plt.title('Confusion Matrix')
plt.ylabel('Actual')
plt.xlabel('Predicted')
plt.show()

# Calculate additional metrics
tn, fp, fn, tp = cm.ravel()
precision = tp / (tp + fp)
recall = tp / (tp + fn)
f1_score = 2 * (precision * recall) / (precision + recall)
specificity = tn / (tn + fp)

print(f"\nAdditional Metrics:")
print(f"Precision: {precision:.4f}")
print(f"Recall (Sensitivity): {recall:.4f}")
print(f"F1-Score: {f1_score:.4f}")
print(f"Specificity: {specificity:.4f}")

# Make Predictions on Test Set
print("Making predictions on test set...")

# Prepare test data
X_test_scaled = scaler.transform(test_processed)

# Make predictions
test_predictions = final_model.predict(X_test_scaled)
test_probabilities = final_model.predict_proba(X_test_scaled)[:, 1]

# Create submission dataframe
submission = pd.DataFrame({
    'Customer_ID': test_df['Customer_ID'],
    'Churn_Prediction': test_predictions,
    'Churn_Probability': test_probabilities
})

print(f"Test predictions summary:")
print(f"Total customers: {len(submission)}")
print(f"Predicted churners: {submission['Churn_Prediction'].sum()}")
print(f"Predicted churn rate: {submission['Churn_Prediction'].mean():.2%}")
print(f"Average churn probability: {submission['Churn_Probability'].mean():.4f}")

# Save predictions
submission.to_csv('churn_predictions.csv', index=False)
print("\nPredictions saved to 'churn_predictions.csv'")

# Model Persistence
import joblib

# Save the final model and preprocessing objects
joblib.dump(final_model, 'best_churn_model.pkl')
joblib.dump(scaler, 'scaler.pkl')
joblib.dump(encoders, 'label_encoders.pkl')

print("Model and preprocessing objects saved successfully!")
print("Files saved:")
print("- best_churn_model.pkl")
print("- scaler.pkl")
print("- label_encoders.pkl")

# Summary and Recommendations
print("\n" + "="*80)
print("FINAL SUMMARY AND RECOMMENDATIONS")
print("="*80)

print(f"\n🎯 BEST MODEL: {best_final_model_name}")
print(f"🎯 BEST ACCURACY: {best_final_accuracy:.4f} ({best_final_accuracy*100:.2f}%)")

print("\n📊 KEY INSIGHTS:")
print("1. Feature engineering significantly improved model performance")
print("2. Ensemble methods and hyperparameter tuning provided additional gains")
print("3. The model successfully identifies high-risk customers for churn")

print("\n🚀 NEXT STEPS:")
print("1. Deploy the model for real-time churn prediction")
print("2. Implement targeted retention strategies for high-risk customers")
print("3. Monitor model performance and retrain periodically")
print("4. Collect feedback to improve feature engineering")

print("\n✅ DELIVERABLES:")
print("- Trained model saved as 'best_churn_model.pkl'")
print("- Test predictions saved as 'churn_predictions.csv'")
print("- Preprocessing objects saved for future use")

print("\n" + "="*80)
print("ANALYSIS COMPLETE! 🎉")
print("="*80)