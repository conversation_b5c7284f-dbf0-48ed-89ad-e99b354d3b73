#!/usr/bin/env python3
"""
Improved Customer Churn Prediction Script
Uses optimized thresholds and ensemble methods for better accuracy.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_auc_score
from sklearn.utils.class_weight import compute_class_weight
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def preprocess_data(df, is_training=True):
    """Enhanced data preprocessing with feature engineering"""
    df_processed = df.copy()
    
    # Advanced Feature Engineering
    df_processed['Usage_Efficiency'] = df_processed['Total_Usage_Hours'] / (df_processed['Account_Age_Months'] + 1)
    df_processed['Spending_Per_Hour'] = df_processed['Monthly_Spending'] / (df_processed['Total_Usage_Hours'] + 1)
    df_processed['Support_Intensity'] = df_processed['Support_Calls'] / (df_processed['Account_Age_Months'] + 1)
    df_processed['Payment_Reliability'] = 1 / (df_processed['Late_Payments'] + 1)
    df_processed['Engagement_Score'] = (df_processed['Total_Usage_Hours'] * df_processed['Satisfaction_Score']) / (df_processed['Support_Calls'] + 1)
    
    # Interaction features
    df_processed['Age_Spending'] = df_processed['Age'] * df_processed['Monthly_Spending']
    df_processed['Satisfaction_Usage'] = df_processed['Satisfaction_Score'] * df_processed['Total_Usage_Hours']
    df_processed['Support_Late_Ratio'] = df_processed['Support_Calls'] / (df_processed['Late_Payments'] + 1)
    
    # Binning features
    df_processed['Age_Group'] = pd.cut(df_processed['Age'], bins=[0, 25, 35, 50, 100], labels=[0, 1, 2, 3])
    df_processed['Spending_Tier'] = pd.qcut(df_processed['Monthly_Spending'], q=4, labels=[0, 1, 2, 3])
    df_processed['Usage_Tier'] = pd.qcut(df_processed['Total_Usage_Hours'], q=4, labels=[0, 1, 2, 3])
    
    # Handle categorical variables
    categorical_cols = ['Gender', 'Location', 'Subscription_Type', 'Last_Interaction_Type']
    label_encoders = {}
    
    for col in categorical_cols:
        if col in df_processed.columns:
            le = LabelEncoder()
            df_processed[col] = le.fit_transform(df_processed[col].astype(str))
            label_encoders[col] = le
    
    # Remove Customer_ID
    if 'Customer_ID' in df_processed.columns:
        df_processed = df_processed.drop('Customer_ID', axis=1)
    
    return df_processed, label_encoders

def find_optimal_threshold(model, X_val, y_val):
    """Find optimal threshold for classification"""
    y_proba = model.predict_proba(X_val)[:, 1]
    thresholds = np.arange(0.1, 0.9, 0.05)
    best_threshold = 0.5
    best_f1 = 0
    
    for threshold in thresholds:
        y_pred_thresh = (y_proba >= threshold).astype(int)
        
        # Calculate F1 score
        tp = np.sum((y_pred_thresh == 1) & (y_val == 1))
        fp = np.sum((y_pred_thresh == 1) & (y_val == 0))
        fn = np.sum((y_pred_thresh == 0) & (y_val == 1))
        
        if tp + fp > 0 and tp + fn > 0:
            precision = tp / (tp + fp)
            recall = tp / (tp + fn)
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold
    
    return best_threshold

def train_and_evaluate_model(model, X_train, X_val, y_train, y_val, model_name):
    """Train and evaluate a single model with optimal threshold"""
    model.fit(X_train, y_train)
    
    # Find optimal threshold
    optimal_threshold = find_optimal_threshold(model, X_val, y_val)
    
    # Make predictions with optimal threshold
    y_proba = model.predict_proba(X_val)[:, 1]
    y_pred = (y_proba >= optimal_threshold).astype(int)
    
    accuracy = accuracy_score(y_val, y_pred)
    auc = roc_auc_score(y_val, y_proba)
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
    
    print(f"\n{model_name} Results:")
    print(f"Optimal Threshold: {optimal_threshold:.3f}")
    print(f"Validation Accuracy: {accuracy:.4f}")
    print(f"AUC Score: {auc:.4f}")
    print(f"CV Accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
    
    return model, accuracy, auc, cv_scores.mean(), optimal_threshold

def main():
    print("🚀 Starting Improved Customer Churn Prediction")
    print("=" * 60)
    
    # Load data
    print("📊 Loading data...")
    try:
        train_df = pd.read_csv('train.csv')
        test_df = pd.read_csv('test.csv')
    except FileNotFoundError as e:
        print(f"Error: {e}")
        return
    
    print(f"Training data shape: {train_df.shape}")
    print(f"Test data shape: {test_df.shape}")
    print(f"Churn rate: {train_df['Churn'].mean():.2%}")
    
    # Preprocess data
    print("\n🔧 Preprocessing data...")
    train_processed, encoders = preprocess_data(train_df, is_training=True)
    test_processed, _ = preprocess_data(test_df, is_training=False)
    
    # Prepare features and target
    X = train_processed.drop('Churn', axis=1)
    y = train_processed['Churn']
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
    
    # Feature scaling
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    print(f"Features: {X.shape[1]}")
    print(f"Training samples: {X_train.shape[0]}")
    print(f"Validation samples: {X_val.shape[0]}")
    
    # Calculate class weights for imbalanced data
    class_weights = compute_class_weight('balanced', classes=np.unique(y_train), y=y_train)
    class_weight_dict = {0: class_weights[0], 1: class_weights[1]}
    
    # Train multiple models with class balancing
    print("\n🤖 Training balanced models...")
    results = {}
    
    # Models with class balancing
    models = {
        'Balanced Logistic Regression': LogisticRegression(
            random_state=42, max_iter=1000, class_weight='balanced'
        ),
        'Balanced Random Forest': RandomForestClassifier(
            n_estimators=200, random_state=42, n_jobs=-1, class_weight='balanced'
        ),
        'Balanced Gradient Boosting': GradientBoostingClassifier(
            random_state=42, n_estimators=200
        )
    }
    
    for name, model in models.items():
        trained_model, acc, auc, cv, threshold = train_and_evaluate_model(
            model, X_train_scaled, X_val_scaled, y_train, y_val, name
        )
        results[name] = {
            'model': trained_model, 
            'accuracy': acc, 
            'auc': auc, 
            'cv_score': cv,
            'threshold': threshold
        }
    
    # Find best model
    best_model_name = max(results, key=lambda x: results[x]['auc'])  # Use AUC for imbalanced data
    best_model_info = results[best_model_name]
    best_model = best_model_info['model']
    best_threshold = best_model_info['threshold']
    
    print(f"\n🏆 Best Model: {best_model_name}")
    print(f"🏆 Best AUC: {best_model_info['auc']:.4f}")
    print(f"🏆 Best Accuracy: {best_model_info['accuracy']:.4f} ({best_model_info['accuracy']*100:.2f}%)")
    
    # Make predictions on test set
    print("\n📈 Making predictions on test set...")
    X_test_scaled = scaler.transform(test_processed)
    test_probabilities = best_model.predict_proba(X_test_scaled)[:, 1]
    test_predictions = (test_probabilities >= best_threshold).astype(int)
    
    # Create comprehensive submission
    submission = pd.DataFrame({
        'Customer_ID': test_df['Customer_ID'],
        'Churn_Prediction': test_predictions,
        'Churn_Probability': test_probabilities.round(4)
    })
    
    # Create detailed submission with customer info
    detailed_submission = pd.DataFrame({
        'Customer_ID': test_df['Customer_ID'],
        'Age': test_df['Age'],
        'Gender': test_df['Gender'],
        'Location': test_df['Location'],
        'Subscription_Type': test_df['Subscription_Type'],
        'Monthly_Spending': test_df['Monthly_Spending'],
        'Satisfaction_Score': test_df['Satisfaction_Score'],
        'Support_Calls': test_df['Support_Calls'],
        'Late_Payments': test_df['Late_Payments'],
        'Churn_Prediction': test_predictions,
        'Churn_Probability': test_probabilities.round(4),
        'Risk_Level': ['High' if p >= 0.7 else 'Medium' if p >= 0.3 else 'Low' for p in test_probabilities]
    })
    
    # Save files
    submission.to_csv('improved_churn_predictions.csv', index=False)
    detailed_submission.to_csv('detailed_improved_predictions.csv', index=False)
    
    print(f"\n✅ Results:")
    print(f"- Total test customers: {len(submission)}")
    print(f"- Predicted churners: {submission['Churn_Prediction'].sum()}")
    print(f"- Predicted churn rate: {submission['Churn_Prediction'].mean():.2%}")
    print(f"- Average churn probability: {submission['Churn_Probability'].mean():.4f}")
    print(f"- High risk customers: {sum(detailed_submission['Risk_Level'] == 'High')}")
    print(f"- Medium risk customers: {sum(detailed_submission['Risk_Level'] == 'Medium')}")
    print(f"- Low risk customers: {sum(detailed_submission['Risk_Level'] == 'Low')}")
    print(f"- Files saved: improved_churn_predictions.csv, detailed_improved_predictions.csv")
    
    print("\n🎉 Improved Analysis Complete!")
    print("=" * 60)

if __name__ == "__main__":
    main()
